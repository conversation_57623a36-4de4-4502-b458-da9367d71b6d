-- Fix column ambiguity in create_changelog_from_commit function
-- This resolves the "column reference \"commit_hash\" is ambiguous" error

-- Drop the existing function first
DROP FUNCTION IF EXISTS create_changelog_from_commit(TEXT, TEXT, TEXT, TIMESTAMP WITH TIME ZONE);

-- Create the new function with fixed parameter names
CREATE OR REPLACE FUNCTION create_changelog_from_commit(
    p_commit_hash TEXT,
    p_commit_message TEXT,
    p_commit_author TEXT DEFAULT 'OnlyDiary Team',
    p_commit_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS UUID AS $$
DECLARE
    changelog_user_id UUID;
    new_entry_id UUID;
    entry_title TEXT;
    entry_content TEXT;
    existing_commit TEXT;
BEGIN
    -- Check if this commit was already processed
    SELECT cc.commit_hash INTO existing_commit 
    FROM changelog_commits cc
    WHERE cc.commit_hash = p_commit_hash;
    
    IF existing_commit IS NOT NULL THEN
        RAISE NOTICE 'Commit % already processed, skipping', p_commit_hash;
        RETURN NULL;
    END IF;
    
    -- Get the ChangeLog user ID
    SELECT get_changelog_user_id() INTO changelog_user_id;
    
    IF changelog_user_id IS NULL THEN
        RAISE EXCEPTION 'ChangeLog user not found. Please run the setup migration first.';
    END IF;
    
    -- Generate user-friendly title from commit message
    entry_title := 'Platform Update - ' || TO_CHAR(p_commit_date, 'Month DD, YYYY');
    
    -- Generate user-friendly content
    entry_content := '# ' || entry_title || E'\n\n';
    entry_content := entry_content || 'Hey everyone! 👋' || E'\n\n';
    entry_content := entry_content || 'Just pushed an update to make OnlyDiary better:' || E'\n\n';
    entry_content := entry_content || '## What Changed' || E'\n\n';
    entry_content := entry_content || '✨ ' || p_commit_message || E'\n\n';
    entry_content := entry_content || '## Technical Details' || E'\n\n';
    entry_content := entry_content || '- **Author**: ' || p_commit_author || E'\n';
    entry_content := entry_content || '- **Commit**: `' || LEFT(p_commit_hash, 8) || '`' || E'\n';
    entry_content := entry_content || '- **Date**: ' || TO_CHAR(p_commit_date, 'YYYY-MM-DD HH24:MI:SS TZ') || E'\n\n';
    entry_content := entry_content || 'These improvements are live now! No action needed on your part.' || E'\n\n';
    entry_content := entry_content || '---' || E'\n';
    entry_content := entry_content || '*Have feedback or suggestions? Feel free to reach out!*';
    
    -- Create the new entry
    INSERT INTO diary_entries (
        user_id,
        title,
        body_md,
        is_free,
        is_hidden,
        created_at,
        updated_at
    ) VALUES (
        changelog_user_id,
        entry_title,
        entry_content,
        true, -- Always free
        false, -- Not hidden
        p_commit_date,
        NOW()
    ) RETURNING id INTO new_entry_id;
    
    -- Record that we processed this commit
    INSERT INTO changelog_commits (
        commit_hash,
        commit_message,
        entry_id,
        processed_at
    ) VALUES (
        p_commit_hash,
        p_commit_message,
        new_entry_id,
        NOW()
    );
    
    RAISE NOTICE 'Created changelog entry % for commit %', new_entry_id, p_commit_hash;
    
    RETURN new_entry_id;
END;
$$ LANGUAGE plpgsql;

-- Update function permissions
GRANT EXECUTE ON FUNCTION create_changelog_from_commit(TEXT, TEXT, TEXT, TIMESTAMP WITH TIME ZONE) TO authenticated;

-- Test the function to make sure it works
SELECT 'Changelog function fixed successfully!' as status;
